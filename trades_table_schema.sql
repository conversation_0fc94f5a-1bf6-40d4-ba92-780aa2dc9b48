-- Trades Table Schema for Trading Bot Server
-- Run this in your Supabase SQL editor

-- Create trades table
CREATE TABLE IF NOT EXISTS public.trades (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    bot_id UUID REFERENCES public.trading_bots(id) ON DELETE CASCADE NOT NULL,
    coin_id TEXT NOT NULL,
    trade_type TEXT NOT NULL CHECK (trade_type IN ('buy', 'sell')),
    quantity DECIMAL(20, 8) NOT NULL,
    price DECIMAL(20, 8) NOT NULL,
    total_amount DECIMAL(20, 8) NOT NULL,
    fee DECIMAL(20, 8) DEFAULT 0,
    indicator_data JSONB DEFAULT '{}'::jsonb,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_trades_bot_id ON public.trades(bot_id);
CREATE INDEX IF NOT EXISTS idx_trades_coin_id ON public.trades(coin_id);
CREATE INDEX IF NOT EXISTS idx_trades_executed_at ON public.trades(executed_at);
CREATE INDEX IF NOT EXISTS idx_trades_trade_type ON public.trades(trade_type);
CREATE INDEX IF NOT EXISTS idx_trades_bot_coin ON public.trades(bot_id, coin_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.trades ENABLE ROW LEVEL SECURITY;

-- RLS Policies for trades table
-- Users can only see trades from their own bots
CREATE POLICY "Users can view their own bot trades" ON public.trades
    FOR SELECT USING (
        bot_id IN (
            SELECT id FROM public.trading_bots 
            WHERE user_id = auth.uid()
        )
    );

-- Users can insert trades for their own bots (for manual trades if needed)
CREATE POLICY "Users can insert trades for their bots" ON public.trades
    FOR INSERT WITH CHECK (
        bot_id IN (
            SELECT id FROM public.trading_bots 
            WHERE user_id = auth.uid()
        )
    );

-- Users can update trades for their own bots
CREATE POLICY "Users can update their own bot trades" ON public.trades
    FOR UPDATE USING (
        bot_id IN (
            SELECT id FROM public.trading_bots 
            WHERE user_id = auth.uid()
        )
    );

-- Users can delete trades for their own bots
CREATE POLICY "Users can delete their own bot trades" ON public.trades
    FOR DELETE USING (
        bot_id IN (
            SELECT id FROM public.trading_bots 
            WHERE user_id = auth.uid()
        )
    );

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_trades_updated_at 
    BEFORE UPDATE ON public.trades 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create a view for trade analytics
CREATE OR REPLACE VIEW public.trade_analytics AS
SELECT 
    t.bot_id,
    tb.user_id,
    tb.name as bot_name,
    tb.coin_symbol,
    COUNT(*) as total_trades,
    COUNT(CASE WHEN t.trade_type = 'buy' THEN 1 END) as buy_trades,
    COUNT(CASE WHEN t.trade_type = 'sell' THEN 1 END) as sell_trades,
    SUM(t.total_amount) as total_volume,
    SUM(t.fee) as total_fees,
    AVG(t.price) as avg_price,
    MIN(t.executed_at) as first_trade,
    MAX(t.executed_at) as last_trade,
    -- Calculate simple P&L (this is simplified - real P&L calculation is more complex)
    SUM(CASE 
        WHEN t.trade_type = 'sell' THEN t.total_amount 
        WHEN t.trade_type = 'buy' THEN -t.total_amount 
    END) as simple_pnl
FROM public.trades t
JOIN public.trading_bots tb ON t.bot_id = tb.id
GROUP BY t.bot_id, tb.user_id, tb.name, tb.coin_symbol;

-- Grant permissions on the view
ALTER VIEW public.trade_analytics OWNER TO postgres;

-- RLS policy for the view
CREATE POLICY "Users can view their own trade analytics" ON public.trade_analytics
    FOR SELECT USING (user_id = auth.uid());

-- Enable RLS on the view
ALTER VIEW public.trade_analytics ENABLE ROW LEVEL SECURITY;

-- Create function to get bot performance metrics
CREATE OR REPLACE FUNCTION get_bot_performance(bot_uuid UUID)
RETURNS TABLE (
    total_trades INTEGER,
    win_trades INTEGER,
    loss_trades INTEGER,
    win_rate DECIMAL,
    total_pnl DECIMAL,
    total_fees DECIMAL,
    avg_trade_amount DECIMAL,
    best_trade DECIMAL,
    worst_trade DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    WITH trade_pnl AS (
        SELECT 
            t.*,
            -- Calculate P&L for each trade pair (simplified)
            CASE 
                WHEN t.trade_type = 'sell' THEN 
                    t.total_amount - (
                        SELECT AVG(t2.total_amount) 
                        FROM trades t2 
                        WHERE t2.bot_id = t.bot_id 
                        AND t2.coin_id = t.coin_id 
                        AND t2.trade_type = 'buy' 
                        AND t2.executed_at < t.executed_at
                    )
                ELSE 0
            END as trade_pnl
        FROM trades t
        WHERE t.bot_id = bot_uuid
    )
    SELECT 
        COUNT(*)::INTEGER as total_trades,
        COUNT(CASE WHEN trade_pnl > 0 THEN 1 END)::INTEGER as win_trades,
        COUNT(CASE WHEN trade_pnl < 0 THEN 1 END)::INTEGER as loss_trades,
        CASE 
            WHEN COUNT(*) > 0 THEN 
                ROUND((COUNT(CASE WHEN trade_pnl > 0 THEN 1 END)::DECIMAL / COUNT(*)::DECIMAL) * 100, 2)
            ELSE 0
        END as win_rate,
        COALESCE(SUM(trade_pnl), 0) as total_pnl,
        COALESCE(SUM(fee), 0) as total_fees,
        COALESCE(AVG(total_amount), 0) as avg_trade_amount,
        COALESCE(MAX(trade_pnl), 0) as best_trade,
        COALESCE(MIN(trade_pnl), 0) as worst_trade
    FROM trade_pnl;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION get_bot_performance(UUID) TO authenticated;

-- Create indexes on tokens table for better performance (if not exists)
CREATE INDEX IF NOT EXISTS idx_tokens_coin_id ON public.tokens(coin_id);
CREATE INDEX IF NOT EXISTS idx_tokens_symbol ON public.tokens(symbol);
CREATE INDEX IF NOT EXISTS idx_tokens_updated_at ON public.tokens(updated_at);

-- Add comment to document the schema
COMMENT ON TABLE public.trades IS 'Stores all trading bot transactions with technical indicator data';
COMMENT ON COLUMN public.trades.indicator_data IS 'JSON data containing technical indicator values at time of trade';
COMMENT ON COLUMN public.trades.quantity IS 'Amount of tokens bought/sold';
COMMENT ON COLUMN public.trades.total_amount IS 'Total USD value of the trade';
COMMENT ON COLUMN public.trades.fee IS 'Trading fee paid for the transaction';
