create table
  public.tokens (
    id uuid not null default gen_random_uuid (),
    coin_id text null,
    name text null,
    symbol text null,
    address text null,
    price double precision null,
    market_cap double precision null,
    liquidity double precision null,
    created_at timestamp with time zone null,
    data jsonb null,
    pair_created timestamp with time zone null,
    chain_id text null,
    dex_type text null,
    image_url text null,
    extra_data jsonb null,
    volume_h24 numeric null,
    price_change_h24 numeric null,
    updated_at timestamp with time zone null,
    constraint tokens_pkey primary key (id),
    constraint tokens_coin_id_key unique (coin_id)
  ) tablespace pg_default;
  
  
  
  
  create table
  public.latest_tokens (
    id uuid not null default gen_random_uuid (),
    name text null,
    token text null,
    dex_type text null,
    pool_created_at timestamp with time zone null,
    fetched_at timestamp with time zone null,
    status integer null,
    constraint latest_tokens_pkey primary key (id),
    constraint latest_tokens_token_key unique (token)
  ) tablespace pg_default;
  
  
  
  create table
  public.trading_bots (
    id uuid not null default gen_random_uuid (),
    user_id uuid not null,
    name text not null,
    coin_id text not null,
    coin_symbol text not null,
    coin_name text not null,
    coin_image text null,
    strategy text not null,
    investment_amount numeric(20, 8) not null,
    stop_loss numeric(5, 2) null,
    take_profit numeric(5, 2) null,
    indicators jsonb null default '[]'::jsonb,
    is_active boolean null default false,
    is_archived boolean null default false,
    total_trades integer null default 0,
    win_rate numeric(5, 2) null default 0,
    total_earned numeric(20, 8) null default 0,
    created_at timestamp with time zone null default now(),
    updated_at timestamp with time zone null default now(),
    constraint trading_bots_pkey primary key (id),
    constraint trading_bots_user_id_fkey foreign key (user_id) references auth.users (id) on delete cascade
  ) tablespace pg_default;

create index if not exists idx_trading_bots_user_id on public.trading_bots using btree (user_id) tablespace pg_default;

create index if not exists idx_trading_bots_active on public.trading_bots using btree (user_id, is_active) tablespace pg_default;

create trigger handle_updated_at before
update on trading_bots for each row
execute function handle_updated_at ();


create table
  public.trades (
    id uuid not null default gen_random_uuid (),
    user_id uuid not null,
    bot_id uuid null,
    coin_id text not null,
    coin_symbol text not null,
    coin_name text not null,
    coin_image text null,
    amount numeric(20, 8) not null,
    entry_price numeric(20, 8) not null,
    current_price numeric(20, 8) null,
    exit_price numeric(20, 8) null,
    entry_time timestamp with time zone not null,
    exit_time timestamp with time zone null,
    last_check timestamp with time zone null default now(),
    status text not null,
    signal text not null,
    indicators text[] null default '{}'::text[],
    profit_loss numeric(20, 8) null default 0,
    profit_loss_percentage numeric(8, 4) null default 0,
    duration text null,
    is_profit boolean null default false,
    created_at timestamp with time zone null default now(),
    updated_at timestamp with time zone null default now(),
    constraint trades_pkey primary key (id),
    constraint trades_user_id_fkey foreign key (user_id) references auth.users (id) on delete cascade,
    constraint trades_bot_id_fkey foreign key (bot_id) references trading_bots (id) on delete cascade,
    constraint trades_status_check check (
      (
        status = any (
          array[
            'active'::text,
            'watching'::text,
            'archived'::text
          ]
        )
      )
    ),
    constraint trades_signal_check check (
      (
        signal = any (array['buy'::text, 'sell'::text, 'hold'::text])
      )
    )
  ) tablespace pg_default;

create index if not exists idx_trades_user_id on public.trades using btree (user_id) tablespace pg_default;

create index if not exists idx_trades_bot_id on public.trades using btree (bot_id) tablespace pg_default;

create index if not exists idx_trades_status on public.trades using btree (user_id, status) tablespace pg_default;

create trigger handle_updated_at before
update on trades for each row
execute function handle_updated_at ();