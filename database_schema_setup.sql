-- Trading Bot Database Schema Setup
-- Run this script in your Supabase SQL editor to ensure all required columns exist

-- First, let's check if the table exists and create it if it doesn't
CREATE TABLE IF NOT EXISTS public.trading_bots (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    coin_id TEXT NOT NULL,
    coin_symbol TEXT NOT NULL,
    coin_name TEXT NOT NULL,
    coin_image TEXT,
    strategy TEXT NOT NULL,
    investment_amount DECIMAL(20, 8) NOT NULL,
    stop_loss DECIMAL(5, 2),
    take_profit DECIMAL(5, 2),
    indicators JSONB DEFAULT '[]'::jsonb,
    is_active BOOLEAN DEFAULT FALSE,
    is_archived BOOLEAN DEFAULT FALSE,
    total_trades INTEGER DEFAULT 0,
    win_rate DECIMAL(5, 2) DEFAULT 0,
    total_earned DECIMAL(20, 8) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add the new columns that were missing
ALTER TABLE public.trading_bots 
ADD COLUMN IF NOT EXISTS start_immediately BOOLEAN DEFAULT FALSE;

ALTER TABLE public.trading_bots 
ADD COLUMN IF NOT EXISTS risk_percentage DECIMAL(5, 2) DEFAULT 2.0;

ALTER TABLE public.trading_bots 
ADD COLUMN IF NOT EXISTS trading_24_7 BOOLEAN DEFAULT TRUE;

ALTER TABLE public.trading_bots 
ADD COLUMN IF NOT EXISTS trading_start_time TIME;

ALTER TABLE public.trading_bots
ADD COLUMN IF NOT EXISTS trading_end_time TIME;

ALTER TABLE public.trading_bots
ADD COLUMN IF NOT EXISTS max_daily_trades INTEGER DEFAULT 50;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_trading_bots_user_id ON public.trading_bots(user_id);
CREATE INDEX IF NOT EXISTS idx_trading_bots_is_active ON public.trading_bots(is_active);
CREATE INDEX IF NOT EXISTS idx_trading_bots_created_at ON public.trading_bots(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE public.trading_bots ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Policy for users to see only their own trading bots
DROP POLICY IF EXISTS "Users can view own trading bots" ON public.trading_bots;
CREATE POLICY "Users can view own trading bots" ON public.trading_bots
    FOR SELECT USING (auth.uid() = user_id);

-- Policy for users to insert their own trading bots
DROP POLICY IF EXISTS "Users can insert own trading bots" ON public.trading_bots;
CREATE POLICY "Users can insert own trading bots" ON public.trading_bots
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy for users to update their own trading bots
DROP POLICY IF EXISTS "Users can update own trading bots" ON public.trading_bots;
CREATE POLICY "Users can update own trading bots" ON public.trading_bots
    FOR UPDATE USING (auth.uid() = user_id);

-- Policy for users to delete their own trading bots
DROP POLICY IF EXISTS "Users can delete own trading bots" ON public.trading_bots;
CREATE POLICY "Users can delete own trading bots" ON public.trading_bots
    FOR DELETE USING (auth.uid() = user_id);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_trading_bots_updated_at ON public.trading_bots;
CREATE TRIGGER update_trading_bots_updated_at
    BEFORE UPDATE ON public.trading_bots
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Verify the table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_schema = 'public' AND table_name = 'trading_bots'
ORDER BY ordinal_position;

-- Test query to verify everything works
-- This should return an empty result set but no errors
SELECT * FROM public.trading_bots WHERE user_id = auth.uid() LIMIT 1;
