#!/bin/bash

# Trading Bot Server Deployment Script
# This script helps deploy the trading bot server to various environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    log_success "All dependencies are installed."
}

# Create environment file if it doesn't exist
setup_environment() {
    log_info "Setting up environment..."
    
    if [ ! -f .env ]; then
        log_warning ".env file not found. Creating from template..."
        cp .env.example .env
        log_warning "Please edit .env file with your configuration before continuing."
        read -p "Press Enter to continue after editing .env file..."
    fi
    
    # Validate required environment variables
    source .env
    
    if [ -z "$DATABASE_URL" ] && [ -z "$SUPABASE_URL" ]; then
        log_error "Either DATABASE_URL or SUPABASE_URL must be set in .env file"
        exit 1
    fi
    
    if [ -n "$SUPABASE_URL" ] && [ -z "$SUPABASE_SERVICE_ROLE_KEY" ]; then
        log_error "SUPABASE_SERVICE_ROLE_KEY must be set when using Supabase"
        exit 1
    fi
    
    log_success "Environment configuration validated."
}

# Setup database schema
setup_database() {
    log_info "Setting up database schema..."
    
    if [ -n "$SUPABASE_URL" ]; then
        log_info "Please run the SQL scripts in your Supabase dashboard:"
        log_info "1. database_schema_setup.sql"
        log_info "2. trades_table_schema.sql"
        read -p "Press Enter after running the SQL scripts..."
    else
        log_info "For PostgreSQL, you can run the scripts manually or use a migration tool."
    fi
    
    log_success "Database setup completed."
}

# Build and start services
deploy_services() {
    local environment=${1:-production}
    
    log_info "Deploying trading bot server for $environment environment..."
    
    # Set environment
    export ENVIRONMENT=$environment
    
    # Build and start services
    docker-compose down --remove-orphans
    docker-compose build --no-cache
    docker-compose up -d
    
    log_success "Services deployed successfully."
}

# Check service health
check_health() {
    log_info "Checking service health..."
    
    # Wait for services to start
    sleep 10
    
    # Check trading bot service
    if docker-compose ps trading-bot | grep -q "Up"; then
        log_success "Trading bot service is running."
    else
        log_error "Trading bot service failed to start."
        docker-compose logs trading-bot
        exit 1
    fi
    
    # Check metrics endpoint
    if curl -f http://localhost:8000/health &> /dev/null; then
        log_success "Health check endpoint is responding."
    else
        log_warning "Health check endpoint is not responding yet."
    fi
}

# Show logs
show_logs() {
    log_info "Showing service logs..."
    docker-compose logs -f trading-bot
}

# Stop services
stop_services() {
    log_info "Stopping services..."
    docker-compose down
    log_success "Services stopped."
}

# Update services
update_services() {
    log_info "Updating services..."
    
    # Pull latest changes
    git pull origin main
    
    # Rebuild and restart
    docker-compose down
    docker-compose build --no-cache
    docker-compose up -d
    
    log_success "Services updated successfully."
}

# Backup data
backup_data() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    
    log_info "Creating backup in $backup_dir..."
    
    mkdir -p "$backup_dir"
    
    # Backup logs
    if [ -d "logs" ]; then
        cp -r logs "$backup_dir/"
    fi
    
    # Backup configuration
    cp .env "$backup_dir/"
    
    # Backup docker volumes (if any)
    docker-compose exec -T trading-bot tar czf - /app/data 2>/dev/null > "$backup_dir/app_data.tar.gz" || true
    
    log_success "Backup created in $backup_dir"
}

# Show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  deploy [env]    Deploy the trading bot server (env: development|production|testing)"
    echo "  start           Start the services"
    echo "  stop            Stop the services"
    echo "  restart         Restart the services"
    echo "  logs            Show service logs"
    echo "  status          Show service status"
    echo "  update          Update and restart services"
    echo "  backup          Create a backup of data and configuration"
    echo "  health          Check service health"
    echo "  setup           Setup environment and database"
    echo ""
    echo "Examples:"
    echo "  $0 deploy production"
    echo "  $0 logs"
    echo "  $0 status"
}

# Main script logic
main() {
    local command=${1:-help}
    local environment=${2:-production}
    
    case $command in
        "deploy")
            check_dependencies
            setup_environment
            setup_database
            deploy_services "$environment"
            check_health
            ;;
        "start")
            docker-compose up -d
            log_success "Services started."
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            docker-compose restart
            log_success "Services restarted."
            ;;
        "logs")
            show_logs
            ;;
        "status")
            docker-compose ps
            ;;
        "update")
            update_services
            ;;
        "backup")
            backup_data
            ;;
        "health")
            check_health
            ;;
        "setup")
            check_dependencies
            setup_environment
            setup_database
            ;;
        "help"|*)
            show_usage
            ;;
    esac
}

# Run main function with all arguments
main "$@"
