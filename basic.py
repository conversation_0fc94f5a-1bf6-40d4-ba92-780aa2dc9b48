from supabase import create_client, Client
from datetime import datetime, timezone, timedelta
import requests, pandas as pd, pandas_ta as ta, schedule, time
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
from config import *
import os
from dotenv import load_dotenv

load_dotenv()  # ✅ Load variables from .env file
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not all([SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY]):
    raise RuntimeError("❌ Missing one or more required environment variables.")

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)



IST = timezone(timedelta(hours=5, minutes=30))
MAX_WORKERS = 8
SERVERS = [
    #"localhost:1150/ohlcv",
    "two.sharfraz.com/ohlcv",
    #"three.sharfraz.com/ohlcv",
    "four.sharfraz.com/ohlcv",
    "one.sharfraz.com/ohlcv",
]

coins_table = TOKENS_COLLECTION

def get_ohlcv_scaled(ohlcv, supply):
    if not ohlcv or supply is None:
        return []

    timestamps = [
        datetime.fromtimestamp(row[0], tz=timezone.utc).astimezone(IST)
        for row in reversed(ohlcv) if len(row) >= 6
    ]

    scaled_ohlcv = [
        [
            ts.strftime('%Y-%m-%d %H:%M:%S'),  # timestamp as string
            row[1] * supply,
            row[2] * supply,
            row[3] * supply,
            row[4] * supply,
            row[5]
        ]
        for ts, row in zip(timestamps, reversed(ohlcv)) if len(row) >= 6
    ]

    df = pd.DataFrame({
        "timestamp": timestamps,
        "open": [row[1] for row in scaled_ohlcv],
        "high": [row[2] for row in scaled_ohlcv],
        "low": [row[3] for row in scaled_ohlcv],
        "close": [row[4] for row in scaled_ohlcv],
        "volume": [row[5] for row in scaled_ohlcv],
    })

    # Sort by datetime just in case (ascending order is required for VWAP)
    df.sort_values("timestamp", inplace=True)
    df["timestamp"] = df["timestamp"].dt.tz_localize(None)
    df.set_index("timestamp", inplace=True)

    # Reattach indicators to each row
    for i, row in enumerate(scaled_ohlcv):
        timestamp_str = row[0]
        ts = df.index[i]

    scaled_ohlcv.reverse()
    return scaled_ohlcv

def coin_prices(address, coin_id, server):
    try:
        res = requests.post(f"http://{server}", data={"coin_id": coin_id, "address": address})
        res.raise_for_status()
        payload = res.json()
        ohlcv = payload["data"]["attributes"]["ohlcv_list"]
        supply = payload.get("supply", 0)
        scaled = get_ohlcv_scaled(ohlcv, supply)

        closes = [r[4] for r in scaled if len(r) >=4][::-1]
        highs = [r[2] for r in scaled if len(r) >= 4][::-1]
        lows = [r[3] for r in scaled if len(r) >= 4][::-1]
        return closes, highs, lows, scaled
    except Exception as e:
        print("Price fetch error:", e)
        return [], [], [], []

def process_token(token, server):
    now = datetime.now(IST)
    coin_id = token["coin_id"]
    address = token["address"]
    symbol = token.get("symbol", "")

    print(f"Processing {symbol} via {server}")
    closes, highs, lows, scaled = coin_prices(address, coin_id, server)
    if not closes:
        return

    ath, atl = max(highs), min(lows)
    extra = {
        "timestamp": now.isoformat(),
        "ath": ath,
        "atl": atl,
        "recent_ohlcv": scaled[:50],

    }

    supabase.table(coins_table).update({"extra_data": extra}).eq("coin_id", coin_id).execute()
    print(f"Updated extra_data for {symbol}")

def run_bot():
    #tokens = supabase.table(coins_table).select("coin_id,address,symbol").eq("coin_id", "GgyYkXoRH9Ja9hdFgFoYDGZw2a9ifBLM4JHmJnEEA5vq").execute().data
    tokens = supabase.table(coins_table).select("coin_id,address,symbol").execute().data

    tasks = []
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        for idx, tk in enumerate(tokens):
            server = SERVERS[(idx // 28) % len(SERVERS)]
            tasks.append(executor.submit(process_token, tk, server))
        for f in as_completed(tasks):
            f.result()  # catch exceptions

schedule.every(60).seconds.do(run_bot)

if __name__ == "__main__":
    print(f"Bot started at {datetime.now(IST)} — running every 60 seconds.")
    run_bot()
    while True:
        schedule.run_pending()
        time.sleep(1)
