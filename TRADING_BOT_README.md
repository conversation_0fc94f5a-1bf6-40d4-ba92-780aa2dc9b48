# Trading Bot Server

An optimized, high-performance Python trading bot server that executes automated trading strategies based on technical indicators. Built for efficiency, scalability, and reliability.

## 🚀 Features

- **High Performance**: Async/await architecture with concurrent bot processing
- **Technical Indicators**: RSI, MACD, Bollinger Bands, EMA using TA-Lib
- **Database Integration**: Full Supabase/PostgreSQL integration with RLS
- **Risk Management**: Position sizing, daily limits, stop-loss/take-profit
- **Real-time Trading**: Processes OHLCV data and executes trades automatically
- **Monitoring**: Prometheus metrics, health checks, comprehensive logging
- **Scalable**: Docker containerization with resource management
- **Secure**: Row Level Security (RLS) and proper authentication

## 📋 Prerequisites

- Python 3.11+
- Docker & Docker Compose
- Supabase account (or PostgreSQL database)
- TA-Lib library

## 🛠 Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd trading-bot-server
```

### 2. Setup Environment

```bash
# Copy environment template
cp .env.example .env

# Edit with your configuration
nano .env
```

### 3. Database Setup

Run these SQL scripts in your Supabase SQL Editor:

```sql
-- 1. First run the main schema
-- database_schema_setup.sql

-- 2. Then run the trades table schema
-- trades_table_schema.sql
```

### 4. Deploy with Docker

```bash
# Make deploy script executable
chmod +x deploy.sh

# Deploy to production
./deploy.sh deploy production

# Or for development
./deploy.sh deploy development
```

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Database (choose one)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-key

# Trading Settings
TRADING_INTERVAL_SECONDS=60
MAX_CONCURRENT_BOTS=100
DEFAULT_TRADING_FEE=0.001

# Risk Management
MAX_POSITION_SIZE=10000
MAX_DAILY_LOSS=1000

# Environment
ENVIRONMENT=production
DRY_RUN=false  # Set to true for testing
```

### Supabase Setup

1. Create a new Supabase project
2. Get your project URL and service role key
3. Run the SQL scripts in SQL Editor
4. Update `.env` with your credentials

## 🏃‍♂️ Running the Server

### Using Docker (Recommended)

```bash
# Deploy and start
./deploy.sh deploy production

# View logs
./deploy.sh logs

# Check status
./deploy.sh status

# Stop services
./deploy.sh stop
```

### Manual Python Execution

```bash
# Install dependencies
pip install -r requirements.txt

# Run the server
python trading_bot_server.py
```

## 📊 How It Works

### 1. Bot Processing Cycle

```
1. Fetch active trading bots from database
2. Get token OHLCV data for each bot
3. Calculate technical indicators (RSI, MACD, etc.)
4. Generate trading signals based on strategy
5. Execute trades and record in database
6. Update bot statistics
7. Wait for next cycle (default: 60 seconds)
```

### 2. Technical Indicators

- **RSI**: Relative Strength Index for overbought/oversold signals
- **MACD**: Moving Average Convergence Divergence for trend changes
- **Bollinger Bands**: Price volatility and support/resistance levels
- **EMA**: Exponential Moving Average for trend direction

### 3. Trading Strategies

- **DCA (Dollar Cost Averaging)**: Regular purchases regardless of price
- **Momentum**: Buy on upward trends, sell on downward trends
- **Mean Reversion**: Buy low, sell high based on indicators

### 4. Risk Management

- Position sizing based on risk percentage
- Daily trade limits per bot
- Stop-loss and take-profit levels
- Maximum position size limits

## 📈 Database Schema

### Trading Bots Table

```sql
trading_bots (
  id, user_id, name, coin_symbol, strategy,
  investment_amount, stop_loss, take_profit,
  indicators, max_daily_trades, is_active, ...
)
```

### Trades Table

```sql
trades (
  id, bot_id, coin_id, trade_type, quantity,
  price, total_amount, fee, indicator_data,
  executed_at, ...
)
```

### Tokens Table

```sql
tokens (
  id, coin_id, symbol, price, market_cap,
  extra_data (OHLCV), recent_ohlcv, ...
)
```

## 🔍 Monitoring

### Health Checks

```bash
# Check if server is running
curl http://localhost:8000/health

# View metrics
curl http://localhost:8000/metrics
```

### Logs

```bash
# View real-time logs
./deploy.sh logs

# Or with Docker
docker-compose logs -f trading-bot
```

### Grafana Dashboard

Access Grafana at `http://localhost:3000` (admin/admin) for:
- Trading performance metrics
- Bot activity monitoring
- System resource usage
- Error tracking

## 🛡 Security

### Row Level Security (RLS)

- Users can only access their own bots and trades
- Service role key used for server operations
- Proper authentication and authorization

### Best Practices

- Never commit secrets to version control
- Use environment variables for all configuration
- Regularly rotate API keys
- Monitor for unusual trading activity
- Set appropriate position and loss limits

## 🚨 Risk Disclaimer

**IMPORTANT**: This is trading software that can execute real trades with real money. 

- **Test thoroughly** in dry-run mode before live trading
- **Start small** with minimal amounts
- **Monitor constantly** during initial deployment
- **Understand the risks** of automated trading
- **Never invest more than you can afford to lose**

## 🔧 Development

### Local Development

```bash
# Set development environment
export ENVIRONMENT=development

# Enable dry run mode
export DRY_RUN=true

# Run with debug logging
export LOG_LEVEL=DEBUG

python trading_bot_server.py
```

### Testing

```bash
# Run tests
pytest tests/

# Run with coverage
pytest --cov=. tests/
```

## 📚 API Reference

### Bot Management

The server automatically processes bots from the database. Configure bots through your Flutter app.

### Indicator Configuration

```json
{
  "type": "rsi",
  "parameters": {"period": 14},
  "buyThreshold": 30,
  "sellThreshold": 70
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the logs: `./deploy.sh logs`
2. Verify configuration: `./deploy.sh health`
3. Review database connectivity
4. Check Supabase RLS policies

## 🔄 Updates

```bash
# Update to latest version
./deploy.sh update

# Backup before updates
./deploy.sh backup
```

---

**Remember**: Always test in dry-run mode first! 🧪
