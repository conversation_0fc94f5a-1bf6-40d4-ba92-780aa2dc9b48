from supabase import create_client, Client
from datetime import datetime, timezone, timedelta
import requests
import json
import time
import schedule
from config import *
import os
from dotenv import load_dotenv

load_dotenv()  # ✅ Load variables from .env file
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not all([SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY]):
    raise RuntimeError("❌ Missing one or more required environment variables.")

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

coins_table = TOKENS_COLLECTION
latest_tokens_table = LATEST_TOKENS

IST = timezone(timedelta(hours=5, minutes=30))


def coinData(coin_id):
    url = f'https://api.dexscreener.com/tokens/v1/solana/{coin_id}'
    try:
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()
    except requests.RequestException as e:
        return {'error': str(e)}

    for i, token_data in enumerate(data):
        liquidity = token_data.get('liquidity', {}).get('usd', '')
        coin_id = token_data.get("baseToken", {}).get("address", "")

        if liquidity == '':
            fallback_url = f'https://api.dexscreener.com/token-pairs/v1/solana/{coin_id}'
            try:
                fallback_response = requests.get(fallback_url)
                fallback_response.raise_for_status()
                fallback_data = fallback_response.json()
            except requests.RequestException as e:
                return {'error': str(e)}

            if isinstance(fallback_data, list) and fallback_data and 'baseToken' in fallback_data[0]:
                data[i] = fallback_data[0]

    return data

def update_token():
    tokens = supabase.table(coins_table).select("coin_id").execute().data
    token_ids = [t["coin_id"] for t in tokens if t.get("coin_id")]

    chunk_size = 30
    token_chunks = [token_ids[i:i + chunk_size] for i in range(0, len(token_ids), chunk_size)]
    token_strings = [','.join(chunk) for chunk in token_chunks]

    for token_str in token_strings:
        coin_data = coinData(token_str)
        for data in coin_data:
            if not isinstance(data, dict):
                print("❌ Invalid Token Data (Not a dict)")
                continue
            if not data:
                print("❌ Empty Token Data")
                continue

            try:
                coin_id = data.get("baseToken", {}).get("address", "")
                dex_type = data.get("dexId", "")
                chain_id = data.get("chainId", "")
                symbol = data.get("baseToken", {}).get("symbol", "")
                print(f"\n📊 Update Processing {symbol.upper()}")
                price = float(data.get("priceUsd", 0))
                market_cap = float(data.get("marketCap", 0))
                liquidity = float(data.get("liquidity", {}).get("usd", 0))
                pairAddress = data.get("pairAddress", "")
                price_change_h24 = data.get("priceChange", {}).get("h24", 0)
                volume_h24 = data.get("volume", {}).get("h24", 0)
                now_ist = datetime.now(IST)

                update_data = {
                    "price": price,
                    "market_cap": market_cap,
                    "liquidity": liquidity,
                    "address": pairAddress,
                    "dex_type": dex_type,
                    "chain_id": chain_id,
                    "updated_at": now_ist.isoformat(),
                    "volume_h24": volume_h24,
                    "price_change_h24": price_change_h24,
                }

                supabase.table(coins_table).update(update_data).eq("coin_id", coin_id).execute()
                print(f"✅ Updated {symbol.upper()}")

            except Exception as e:
                print(f"❌ Error Update processing token: {e}")

def add_token():
    latest_tokens = supabase.table(latest_tokens_table).select("token").eq("status", 1).execute().data
    token_ids = [t["token"] for t in latest_tokens if t.get("token")]

    chunk_size = 30
    token_chunks = [token_ids[i:i + chunk_size] for i in range(0, len(token_ids), chunk_size)]
    token_strings = [','.join(chunk) for chunk in token_chunks]

    for token_str in token_strings:
        coin_data = coinData(token_str)
        for data in coin_data:

            if not isinstance(data, dict):
                print("❌ Invalid Token Data (Not a dict)")
                continue
            if not data:
                print("❌ Empty Token Data")
                continue

            try:
                coin_id = data.get("baseToken", {}).get("address", "")
                symbol = data.get("baseToken", {}).get("symbol", "")
                print(f"\n📊 Add Processing {symbol.upper()}")
                price = float(data.get("priceUsd", 0))
                market_cap = float(data.get("marketCap", 0))
                liquidity = float(data.get("liquidity", {}).get("usd", 0))
                pairAddress = data.get("pairAddress", "")
                name = data.get("baseToken", {}).get("name", "")
                image_url = data.get("info", {}).get("imageUrl", "")
                pair_created = data.get("pairCreatedAt", "")
                timestamp_s = pair_created / 1000
                pair_created = datetime.fromtimestamp(timestamp_s, tz=timezone.utc)
                price_change_h24 = data.get("priceChange", {}).get("h24", 0)
                volume_h24 = data.get("volume", {}).get("h24", 0)

                now_ist = datetime.now(IST)

                if liquidity < 5000:
                    print(f"❌ Minimum Liquidity: {symbol} - ${liquidity}")
                    continue
                if market_cap < 5000:
                    print(f"❌ Minimum MarketCap: {symbol} - ${market_cap}")
                    continue

                token_data = {
                    "coin_id": coin_id,
                    "name": name,
                    "symbol": symbol,
                    "address": pairAddress,
                    "price": price,
                    "market_cap": market_cap,
                    "liquidity": liquidity,
                    "created_at": now_ist.isoformat(),
                    "data": data,
                    "pair_created": pair_created.isoformat(),
                    "image_url": image_url,
                    "volume_h24": volume_h24,
                    "price_change_h24": price_change_h24,
                }

                supabase.table(coins_table).insert(token_data).execute()
                print(f"✅ Inserted: {symbol} - {coin_id}")

                supabase.table("latest_tokens").update({"status": 2}).eq("token", coin_id).execute()
                print(f"✅ Updated latest: {symbol.upper()}")

            except Exception as e:
                print(f"❌ Error Add processing token : {e}")

def run_bot():
    print(f"🚀 Running Coin Data Bot {datetime.now()}")
    add_token()
    update_token()

def job():
    print(f"⏱ Running job at {datetime.now()}")
    run_bot()

schedule.every(5).seconds.do(job)

if __name__ == "__main__":
    print("🚀 Bot scheduler started. Running every 5 seconds.")
    while True:
        schedule.run_pending()
        time.sleep(1)
