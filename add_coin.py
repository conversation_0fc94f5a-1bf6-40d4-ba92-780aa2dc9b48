from supabase import create_client, Client
import requests
from datetime import datetime, timezone, timedelta
import schedule
import time
from config import *
import os
from dotenv import load_dotenv

load_dotenv()  # ✅ Load variables from .env file
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not all([SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY]):
    raise RuntimeError("❌ Missing one or more required environment variables.")

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

IST = timezone(timedelta(hours=5, minutes=30))

latest_tokens = LATEST_TOKENS


# Cleanup old tokens (older than 2 hours)
def cleanup_old_tokens():
    cutoff_time = datetime.now(timezone.utc) - timedelta(hours=2)
    response = supabase.table(latest_tokens).delete().lt("fetched_at", cutoff_time.isoformat()).execute()
    print(f"Deleted {len(response.data)} old token(s) from Supabase.")


# Fetch and store new tokens
def fetch_and_store_tokens():
    cleanup_old_tokens()

    for page in range(1, 11):
        url = f"https://api.geckoterminal.com/api/v2/networks/solana/new_pools?page={page}"
        try:
            response = requests.get(url)
            if response.status_code == 200:
                data = response.json().get("data", [])
                for item in data:
                    attributes = item["attributes"]
                    relationships = item["relationships"]


                    created_at = datetime.fromisoformat(attributes["pool_created_at"].replace("Z", "+00:00"))
                    dex_type = relationships.get("dex", {}).get("data", {}).get("id", "")

                    #if dex_type not in ("pumpswap", "pumpfun", "raydium", "raydium-clmm"):
                    if dex_type not in ("pumpswap", "pumpfun"):
                        continue

                    name = attributes.get("name", "").replace("/ SOL", "").strip()
                    token_id = relationships["base_token"]["data"]["id"].replace("solana_", "")

                    token_data = {
                        "name": name,
                        "token": token_id,
                        "dex_type": dex_type,
                        "pool_created_at": created_at.isoformat(),
                        "fetched_at": datetime.utcnow().isoformat(),
                        "status": 1
                    }

                    try:
                        # Upsert to avoid duplicates based on primary key 'token'
                        supabase.table(latest_tokens).upsert(token_data, on_conflict=["token"]).execute()
                        print(f"✅ Upserted: {name} - {token_id}")
                    except Exception as e:
                        print(f"⏩ Skipped: {name} - {token_id} | Reason: {e}")
            else:
                print(f"❌ Error: Page {page} - {response.status_code}")
        except Exception as e:
            print(f"⚠️ Exception on page {page}: {e}")


# Main job scheduler
def run_bot():
    print(f"🚀 Running Add Coin Bot {datetime.now()}")
    fetch_and_store_tokens()


def job():
    print(f"⏱ Running job at {datetime.now()}")
    run_bot()


# Schedule every 30 seconds
schedule.every(30).seconds.do(job)

if __name__ == "__main__":
    job()
    while True:
        schedule.run_pending()
        time.sleep(1)
