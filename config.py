"""
Configuration settings for Trading Bot Server
"""

import os
from typing import Optional
from pydantic import BaseSettings, Field
from pydantic_settings import SettingsConfigMixin

class TradingBotConfig(BaseSettings, SettingsConfigMixin):
    """Trading bot configuration with environment variable support"""
    
    # Database settings
    database_url: str = Field(
        default="postgresql://user:password@localhost:5432/trading_db",
        env="DATABASE_URL",
        description="PostgreSQL database connection URL"
    )
    
    # Supabase settings (if using Supabase)
    supabase_url: Optional[str] = Field(
        default=None,
        env="SUPABASE_URL",
        description="Supabase project URL"
    )
    
    supabase_key: Optional[str] = Field(
        default=None,
        env="SUPABASE_ANON_KEY",
        description="Supabase anonymous key"
    )
    
    supabase_service_key: Optional[str] = Field(
        default=None,
        env="SUPABASE_SERVICE_ROLE_KEY",
        description="Supabase service role key for server operations"
    )
    
    # Trading settings
    trading_interval_seconds: int = Field(
        default=60,
        env="TRADING_INTERVAL_SECONDS",
        description="Interval between trading cycles in seconds"
    )
    
    max_concurrent_bots: int = Field(
        default=100,
        env="MAX_CONCURRENT_BOTS",
        description="Maximum number of bots to process concurrently"
    )
    
    default_trading_fee: float = Field(
        default=0.001,
        env="DEFAULT_TRADING_FEE",
        description="Default trading fee percentage (0.001 = 0.1%)"
    )
    
    # Risk management
    max_position_size: float = Field(
        default=10000.0,
        env="MAX_POSITION_SIZE",
        description="Maximum position size in USD"
    )
    
    max_daily_loss: float = Field(
        default=1000.0,
        env="MAX_DAILY_LOSS",
        description="Maximum daily loss per bot in USD"
    )
    
    # API settings
    api_rate_limit: int = Field(
        default=100,
        env="API_RATE_LIMIT",
        description="API rate limit per minute"
    )
    
    # Logging settings
    log_level: str = Field(
        default="INFO",
        env="LOG_LEVEL",
        description="Logging level (DEBUG, INFO, WARNING, ERROR)"
    )
    
    log_file: Optional[str] = Field(
        default=None,
        env="LOG_FILE",
        description="Log file path (if None, logs to console)"
    )
    
    # Redis settings (for caching and rate limiting)
    redis_url: Optional[str] = Field(
        default=None,
        env="REDIS_URL",
        description="Redis connection URL for caching"
    )
    
    # Monitoring settings
    enable_metrics: bool = Field(
        default=True,
        env="ENABLE_METRICS",
        description="Enable Prometheus metrics collection"
    )
    
    metrics_port: int = Field(
        default=8000,
        env="METRICS_PORT",
        description="Port for metrics server"
    )
    
    # Development settings
    debug_mode: bool = Field(
        default=False,
        env="DEBUG_MODE",
        description="Enable debug mode"
    )
    
    dry_run: bool = Field(
        default=False,
        env="DRY_RUN",
        description="Run in dry-run mode (no actual trades)"
    )
    
    # Notification settings
    webhook_url: Optional[str] = Field(
        default=None,
        env="WEBHOOK_URL",
        description="Webhook URL for trade notifications"
    )
    
    discord_webhook: Optional[str] = Field(
        default=None,
        env="DISCORD_WEBHOOK",
        description="Discord webhook for notifications"
    )
    
    # Technical indicator settings
    min_ohlcv_periods: int = Field(
        default=50,
        env="MIN_OHLCV_PERIODS",
        description="Minimum OHLCV periods required for indicator calculation"
    )
    
    indicator_cache_ttl: int = Field(
        default=300,
        env="INDICATOR_CACHE_TTL",
        description="Indicator cache TTL in seconds"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

# Global config instance
config = TradingBotConfig()

# Database connection string for asyncpg
def get_database_url() -> str:
    """Get the database URL for asyncpg connection"""
    if config.supabase_url and config.supabase_service_key:
        # For Supabase, construct the connection string
        # Extract the database details from Supabase URL
        import re
        match = re.match(r'https://([^.]+)\.supabase\.co', config.supabase_url)
        if match:
            project_id = match.group(1)
            return f"postgresql://postgres:{config.supabase_service_key}@db.{project_id}.supabase.co:5432/postgres"
    
    return config.database_url

# Logging configuration
def setup_logging():
    """Setup logging configuration"""
    import logging
    import sys
    
    # Set log level
    log_level = getattr(logging, config.log_level.upper(), logging.INFO)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Setup handlers
    handlers = []
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    handlers.append(console_handler)
    
    # File handler (if specified)
    if config.log_file:
        file_handler = logging.FileHandler(config.log_file)
        file_handler.setFormatter(formatter)
        handlers.append(file_handler)
    
    # Configure root logger
    logging.basicConfig(
        level=log_level,
        handlers=handlers,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Suppress noisy loggers
    logging.getLogger('asyncpg').setLevel(logging.WARNING)
    logging.getLogger('aiohttp').setLevel(logging.WARNING)

# Validation functions
def validate_config():
    """Validate configuration settings"""
    errors = []
    
    if not config.database_url and not (config.supabase_url and config.supabase_service_key):
        errors.append("Either DATABASE_URL or SUPABASE_URL + SUPABASE_SERVICE_ROLE_KEY must be provided")
    
    if config.trading_interval_seconds < 10:
        errors.append("Trading interval must be at least 10 seconds")
    
    if config.max_concurrent_bots < 1:
        errors.append("Max concurrent bots must be at least 1")
    
    if config.default_trading_fee < 0 or config.default_trading_fee > 0.1:
        errors.append("Trading fee must be between 0 and 0.1 (10%)")
    
    if errors:
        raise ValueError(f"Configuration errors: {'; '.join(errors)}")

# Environment-specific configurations
class DevelopmentConfig(TradingBotConfig):
    """Development environment configuration"""
    debug_mode: bool = True
    dry_run: bool = True
    trading_interval_seconds: int = 30
    log_level: str = "DEBUG"

class ProductionConfig(TradingBotConfig):
    """Production environment configuration"""
    debug_mode: bool = False
    dry_run: bool = False
    log_level: str = "INFO"
    enable_metrics: bool = True

class TestingConfig(TradingBotConfig):
    """Testing environment configuration"""
    debug_mode: bool = True
    dry_run: bool = True
    trading_interval_seconds: int = 5
    log_level: str = "DEBUG"
    database_url: str = "postgresql://test:test@localhost:5432/test_trading_db"

def get_config(environment: str = None) -> TradingBotConfig:
    """Get configuration based on environment"""
    env = environment or os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionConfig()
    elif env == "testing":
        return TestingConfig()
    else:
        return DevelopmentConfig()

# Export the config
__all__ = ["config", "get_config", "setup_logging", "validate_config", "get_database_url"]
