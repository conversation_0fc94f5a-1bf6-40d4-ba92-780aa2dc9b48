#!/usr/bin/env python3
"""
Optimized Trading Bot Server
Handles automated trading with technical indicators and database management
"""

import asyncio
import asyncpg
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import os
from dataclasses import dataclass
import talib

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class TradingBot:
    id: str
    user_id: str
    name: str
    coin_id: str
    coin_symbol: str
    strategy: str
    investment_amount: float
    stop_loss: float
    take_profit: float
    indicators: List[Dict]
    is_active: bool
    max_daily_trades: int
    risk_percentage: float
    trading_24_7: bool
    trading_start_time: Optional[str]
    trading_end_time: Optional[str]

@dataclass
class TokenData:
    id: str
    coin_id: str
    symbol: str
    price: float
    recent_ohlcv: List[List]
    market_cap: float
    volume_h24: float
    price_change_h24: float

class TechnicalIndicators:
    """Optimized technical indicator calculations using TA-Lib"""
    
    @staticmethod
    def calculate_rsi(prices: np.array, period: int = 14) -> float:
        """Calculate RSI indicator"""
        if len(prices) < period + 1:
            return 50.0  # Neutral RSI if insufficient data
        
        rsi_values = talib.RSI(prices, timeperiod=period)
        return float(rsi_values[-1]) if not np.isnan(rsi_values[-1]) else 50.0
    
    @staticmethod
    def calculate_macd(prices: np.array, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[float, float, float]:
        """Calculate MACD indicator"""
        if len(prices) < slow + signal:
            return 0.0, 0.0, 0.0
        
        macd, macd_signal, macd_hist = talib.MACD(prices, fastperiod=fast, slowperiod=slow, signalperiod=signal)
        
        return (
            float(macd[-1]) if not np.isnan(macd[-1]) else 0.0,
            float(macd_signal[-1]) if not np.isnan(macd_signal[-1]) else 0.0,
            float(macd_hist[-1]) if not np.isnan(macd_hist[-1]) else 0.0
        )
    
    @staticmethod
    def calculate_bollinger_bands(prices: np.array, period: int = 20, std_dev: int = 2) -> Tuple[float, float, float]:
        """Calculate Bollinger Bands"""
        if len(prices) < period:
            current_price = prices[-1] if len(prices) > 0 else 0.0
            return current_price, current_price, current_price
        
        upper, middle, lower = talib.BBANDS(prices, timeperiod=period, nbdevup=std_dev, nbdevdn=std_dev)
        
        return (
            float(upper[-1]) if not np.isnan(upper[-1]) else 0.0,
            float(middle[-1]) if not np.isnan(middle[-1]) else 0.0,
            float(lower[-1]) if not np.isnan(lower[-1]) else 0.0
        )
    
    @staticmethod
    def calculate_ema(prices: np.array, period: int = 20) -> float:
        """Calculate Exponential Moving Average"""
        if len(prices) < period:
            return float(np.mean(prices)) if len(prices) > 0 else 0.0
        
        ema_values = talib.EMA(prices, timeperiod=period)
        return float(ema_values[-1]) if not np.isnan(ema_values[-1]) else 0.0

class TradingEngine:
    """Core trading engine with optimized decision making"""
    
    def __init__(self, db_pool: asyncpg.Pool):
        self.db_pool = db_pool
        self.indicators = TechnicalIndicators()
    
    def parse_ohlcv_data(self, recent_ohlcv: List[List]) -> pd.DataFrame:
        """Parse OHLCV data into pandas DataFrame"""
        if not recent_ohlcv:
            return pd.DataFrame()
        
        df = pd.DataFrame(recent_ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp')
        
        # Convert to numeric
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        return df
    
    def calculate_all_indicators(self, bot: TradingBot, token_data: TokenData) -> Dict:
        """Calculate all indicators for a trading bot"""
        df = self.parse_ohlcv_data(token_data.recent_ohlcv)
        
        if df.empty or len(df) < 10:
            logger.warning(f"Insufficient OHLCV data for {token_data.symbol}")
            return {}
        
        prices = df['close'].values
        highs = df['high'].values
        lows = df['low'].values
        volumes = df['volume'].values
        
        indicator_values = {}
        
        for indicator in bot.indicators:
            indicator_type = indicator.get('type', '').lower()
            params = indicator.get('parameters', {})
            
            try:
                if indicator_type == 'rsi':
                    period = params.get('period', 14)
                    indicator_values['rsi'] = self.indicators.calculate_rsi(prices, period)
                
                elif indicator_type == 'macd':
                    fast = params.get('fastPeriod', 12)
                    slow = params.get('slowPeriod', 26)
                    signal = params.get('signalPeriod', 9)
                    macd, signal_line, histogram = self.indicators.calculate_macd(prices, fast, slow, signal)
                    indicator_values['macd'] = {
                        'macd': macd,
                        'signal': signal_line,
                        'histogram': histogram
                    }
                
                elif indicator_type == 'bollinger':
                    period = params.get('period', 20)
                    std_dev = params.get('standardDeviations', 2)
                    upper, middle, lower = self.indicators.calculate_bollinger_bands(prices, period, std_dev)
                    indicator_values['bollinger'] = {
                        'upper': upper,
                        'middle': middle,
                        'lower': lower
                    }
                
                elif indicator_type == 'ema':
                    period = params.get('period', 20)
                    indicator_values['ema'] = self.indicators.calculate_ema(prices, period)
                    
            except Exception as e:
                logger.error(f"Error calculating {indicator_type}: {e}")
                continue
        
        return indicator_values
    
    def generate_trading_signal(self, bot: TradingBot, token_data: TokenData, indicator_values: Dict) -> str:
        """Generate trading signal based on indicators and strategy"""
        current_price = token_data.price
        signals = []
        
        # RSI signals
        if 'rsi' in indicator_values:
            rsi = indicator_values['rsi']
            rsi_indicator = next((ind for ind in bot.indicators if ind.get('type') == 'rsi'), None)
            if rsi_indicator:
                buy_threshold = rsi_indicator.get('buyThreshold', 30)
                sell_threshold = rsi_indicator.get('sellThreshold', 70)
                
                if rsi <= buy_threshold:
                    signals.append('BUY')
                elif rsi >= sell_threshold:
                    signals.append('SELL')
        
        # MACD signals
        if 'macd' in indicator_values:
            macd_data = indicator_values['macd']
            if macd_data['macd'] > macd_data['signal'] and macd_data['histogram'] > 0:
                signals.append('BUY')
            elif macd_data['macd'] < macd_data['signal'] and macd_data['histogram'] < 0:
                signals.append('SELL')
        
        # Bollinger Bands signals
        if 'bollinger' in indicator_values:
            bb_data = indicator_values['bollinger']
            if current_price <= bb_data['lower']:
                signals.append('BUY')
            elif current_price >= bb_data['upper']:
                signals.append('SELL')
        
        # EMA signals
        if 'ema' in indicator_values:
            ema = indicator_values['ema']
            if current_price > ema * 1.02:  # 2% above EMA
                signals.append('BUY')
            elif current_price < ema * 0.98:  # 2% below EMA
                signals.append('SELL')
        
        # Determine final signal based on strategy
        if bot.strategy.lower() == 'dca':
            # DCA strategy: Buy on any buy signal, ignore sell signals
            return 'BUY' if 'BUY' in signals else 'HOLD'
        else:
            # Other strategies: Majority vote
            buy_count = signals.count('BUY')
            sell_count = signals.count('SELL')
            
            if buy_count > sell_count:
                return 'BUY'
            elif sell_count > buy_count:
                return 'SELL'
            else:
                return 'HOLD'

    async def execute_trade(self, bot: TradingBot, token_data: TokenData, signal: str, indicator_values: Dict) -> Optional[str]:
        """Execute trade and record in database"""
        if signal == 'HOLD':
            return None

        current_price = token_data.price
        trade_amount = bot.investment_amount * (bot.risk_percentage / 100)

        # Calculate quantity based on current price
        if signal == 'BUY':
            quantity = trade_amount / current_price
            trade_type = 'buy'
        else:  # SELL
            # For sell, we need to check if we have holdings
            quantity = await self.get_bot_holdings(bot.id, token_data.coin_id)
            if quantity <= 0:
                logger.info(f"No holdings to sell for bot {bot.id}")
                return None
            trade_type = 'sell'

        # Calculate fees (assuming 0.1% trading fee)
        fee_percentage = 0.001
        fee_amount = trade_amount * fee_percentage

        # Record trade in database
        trade_id = await self.record_trade(
            bot_id=bot.id,
            coin_id=token_data.coin_id,
            trade_type=trade_type,
            quantity=quantity,
            price=current_price,
            total_amount=trade_amount,
            fee=fee_amount,
            indicator_values=indicator_values
        )

        # Update bot statistics
        await self.update_bot_stats(bot.id, trade_type, trade_amount, current_price)

        logger.info(f"Executed {trade_type.upper()} trade for bot {bot.id}: {quantity:.6f} {token_data.symbol} at ${current_price:.6f}")

        return trade_id

    async def get_bot_holdings(self, bot_id: str, coin_id: str) -> float:
        """Get current holdings for a bot and coin"""
        async with self.db_pool.acquire() as conn:
            # Calculate holdings from trade history
            result = await conn.fetchrow("""
                SELECT
                    COALESCE(SUM(CASE WHEN trade_type = 'buy' THEN quantity ELSE -quantity END), 0) as holdings
                FROM trades
                WHERE bot_id = $1 AND coin_id = $2
            """, bot_id, coin_id)

            return float(result['holdings']) if result else 0.0

    async def record_trade(self, bot_id: str, coin_id: str, trade_type: str, quantity: float,
                          price: float, total_amount: float, fee: float, indicator_values: Dict) -> str:
        """Record trade in database"""
        async with self.db_pool.acquire() as conn:
            trade_id = await conn.fetchval("""
                INSERT INTO trades (
                    bot_id, coin_id, trade_type, quantity, price,
                    total_amount, fee, indicator_data, executed_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
                RETURNING id
            """, bot_id, coin_id, trade_type, quantity, price, total_amount, fee, json.dumps(indicator_values))

            return str(trade_id)

    async def update_bot_stats(self, bot_id: str, trade_type: str, amount: float, price: float):
        """Update bot trading statistics"""
        async with self.db_pool.acquire() as conn:
            # Increment total trades
            await conn.execute("""
                UPDATE trading_bots
                SET total_trades = total_trades + 1,
                    updated_at = NOW()
                WHERE id = $1
            """, bot_id)

            # Calculate win rate and total earned (simplified)
            if trade_type == 'sell':
                # This is a simplified calculation - in reality you'd need more complex P&L tracking
                await conn.execute("""
                    UPDATE trading_bots
                    SET total_earned = total_earned + $2
                    WHERE id = $1
                """, bot_id, amount * 0.02)  # Assume 2% profit for demo

class TradingBotManager:
    """Main trading bot manager"""

    def __init__(self):
        self.db_pool = None
        self.trading_engine = None
        self.running = False

    async def initialize(self):
        """Initialize database connection and trading engine"""
        # Database connection
        database_url = os.getenv('DATABASE_URL', 'postgresql://user:password@localhost/trading_db')
        self.db_pool = await asyncpg.create_pool(database_url, min_size=5, max_size=20)

        # Initialize trading engine
        self.trading_engine = TradingEngine(self.db_pool)

        logger.info("Trading bot manager initialized")

    async def get_active_bots(self) -> List[TradingBot]:
        """Get all active trading bots"""
        async with self.db_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT * FROM trading_bots
                WHERE is_active = true AND is_archived = false
            """)

            bots = []
            for row in rows:
                bot = TradingBot(
                    id=str(row['id']),
                    user_id=str(row['user_id']),
                    name=row['name'],
                    coin_id=row['coin_id'],
                    coin_symbol=row['coin_symbol'],
                    strategy=row['strategy'],
                    investment_amount=float(row['investment_amount']),
                    stop_loss=float(row['stop_loss']) if row['stop_loss'] else 0.0,
                    take_profit=float(row['take_profit']) if row['take_profit'] else 0.0,
                    indicators=row['indicators'] if row['indicators'] else [],
                    is_active=row['is_active'],
                    max_daily_trades=row['max_daily_trades'] if row['max_daily_trades'] else 50,
                    risk_percentage=float(row['risk_percentage']) if row['risk_percentage'] else 2.0,
                    trading_24_7=row['trading_24_7'] if row['trading_24_7'] is not None else True,
                    trading_start_time=row['trading_start_time'],
                    trading_end_time=row['trading_end_time']
                )
                bots.append(bot)

            return bots

    async def get_token_data(self, coin_id: str) -> Optional[TokenData]:
        """Get token data from database"""
        async with self.db_pool.acquire() as conn:
            row = await conn.fetchrow("""
                SELECT * FROM tokens WHERE coin_id = $1
            """, coin_id)

            if not row:
                return None

            extra_data = row['extra_data'] if row['extra_data'] else {}
            recent_ohlcv = extra_data.get('recent_ohlcv', [])

            return TokenData(
                id=str(row['id']),
                coin_id=row['coin_id'],
                symbol=row['symbol'],
                price=float(row['price']) if row['price'] else 0.0,
                recent_ohlcv=recent_ohlcv,
                market_cap=float(row['market_cap']) if row['market_cap'] else 0.0,
                volume_h24=float(row['volume_h24']) if row['volume_h24'] else 0.0,
                price_change_h24=float(row['price_change_h24']) if row['price_change_h24'] else 0.0
            )

    def is_trading_time(self, bot: TradingBot) -> bool:
        """Check if it's trading time for the bot"""
        if bot.trading_24_7:
            return True

        if not bot.trading_start_time or not bot.trading_end_time:
            return True

        now = datetime.now().time()
        start_time = datetime.strptime(bot.trading_start_time, '%H:%M').time()
        end_time = datetime.strptime(bot.trading_end_time, '%H:%M').time()

        if start_time <= end_time:
            return start_time <= now <= end_time
        else:  # Overnight trading
            return now >= start_time or now <= end_time

    async def check_daily_trade_limit(self, bot_id: str, max_trades: int) -> bool:
        """Check if bot has reached daily trade limit"""
        async with self.db_pool.acquire() as conn:
            today = datetime.now().date()
            count = await conn.fetchval("""
                SELECT COUNT(*) FROM trades
                WHERE bot_id = $1 AND DATE(executed_at) = $2
            """, bot_id, today)

            return count < max_trades

    async def process_bot(self, bot: TradingBot):
        """Process a single trading bot"""
        try:
            # Check trading time
            if not self.is_trading_time(bot):
                return

            # Check daily trade limit
            if not await self.check_daily_trade_limit(bot.id, bot.max_daily_trades):
                logger.info(f"Bot {bot.id} reached daily trade limit")
                return

            # Get token data
            token_data = await self.get_token_data(bot.coin_id)
            if not token_data:
                logger.warning(f"No token data found for {bot.coin_id}")
                return

            # Calculate indicators
            indicator_values = self.trading_engine.calculate_all_indicators(bot, token_data)
            if not indicator_values:
                logger.warning(f"No indicator values calculated for bot {bot.id}")
                return

            # Generate trading signal
            signal = self.trading_engine.generate_trading_signal(bot, token_data, indicator_values)

            # Execute trade if signal is not HOLD
            if signal != 'HOLD':
                trade_id = await self.trading_engine.execute_trade(bot, token_data, signal, indicator_values)
                if trade_id:
                    logger.info(f"Trade executed for bot {bot.id}: {signal} signal, trade ID: {trade_id}")

        except Exception as e:
            logger.error(f"Error processing bot {bot.id}: {e}")

    async def run_trading_cycle(self):
        """Run one complete trading cycle"""
        logger.info("Starting trading cycle...")

        # Get all active bots
        active_bots = await self.get_active_bots()
        logger.info(f"Processing {len(active_bots)} active bots")

        # Process each bot concurrently
        tasks = [self.process_bot(bot) for bot in active_bots]
        await asyncio.gather(*tasks, return_exceptions=True)

        logger.info("Trading cycle completed")

    async def start(self, interval_seconds: int = 60):
        """Start the trading bot manager"""
        await self.initialize()
        self.running = True

        logger.info(f"Starting trading bot manager with {interval_seconds}s interval")

        while self.running:
            try:
                await self.run_trading_cycle()
                await asyncio.sleep(interval_seconds)
            except Exception as e:
                logger.error(f"Error in trading cycle: {e}")
                await asyncio.sleep(interval_seconds)

    async def stop(self):
        """Stop the trading bot manager"""
        self.running = False
        if self.db_pool:
            await self.db_pool.close()
        logger.info("Trading bot manager stopped")

async def main():
    """Main entry point"""
    manager = TradingBotManager()

    try:
        # Start with 60-second intervals (can be adjusted)
        await manager.start(interval_seconds=60)
    except KeyboardInterrupt:
        logger.info("Received shutdown signal")
    finally:
        await manager.stop()

if __name__ == "__main__":
    asyncio.run(main())
